"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Sparkles, Timer } from "lucide-react";
import { subscriptionService } from "@/services/api/subscriptionService";
import { FEATURES } from "@/config/features";
import { useTranslations } from "next-intl";
import { safeOpenUrl } from "@/lib/browserUtils";
import { trackEvent } from "@/lib/analytics";

export function DashboardPromotionCard() {
  const t = useTranslations("dashboard.promotionCard");
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState({
    minutes: 5,
    seconds: 0,
    milliseconds: 0,
  });
  const [isPromotionActive, setIsPromotionActive] = useState(true);
  const { plan, discountPercentage, promoCode } = FEATURES.PROMO_CARD || {};

  const PROMO_DURATION = 10 * 60 * 1000; // 10分钟的毫秒数

  // 价格计算逻辑
  const getOriginalPrice = (plan) => {
    const prices = {
      basic_yearly: 120,
      basic_monthly: 10,
      pro_yearly: 360,
      pro_monthly: 30,
    };
    return prices[plan] || 120;
  };

  const calculateDiscountedPrice = (originalPrice, discountPercentage) => {
    if (!isPromotionActive) return originalPrice;
    return originalPrice * (1 - discountPercentage / 100);
  };

  const originalPrice = getOriginalPrice(plan);
  const discountedPrice = calculateDiscountedPrice(
    originalPrice,
    discountPercentage
  );
  const isYearlyPlan = plan?.includes("year");

  // 修改检查促销状态逻辑
  const checkPromoStatus = () => {
    const promoStatus = localStorage.getItem("promoStatus");
    const now = Date.now();

    if (!promoStatus) {
      // 首次访问
      const endTime = now + PROMO_DURATION;
      localStorage.setItem(
        "promoStatus",
        JSON.stringify({
          endTime,
          lastPromoTime: now,
        })
      );
      return endTime;
    }

    const { endTime } = JSON.parse(promoStatus);

    // 如果当前时间已经超过了结束时间，计算应该在哪个周期
    if (now > endTime) {
      const elapsedTime = now - endTime;
      const completedCycles = Math.floor(elapsedTime / PROMO_DURATION);
      const newEndTime = endTime + (completedCycles + 1) * PROMO_DURATION;

      localStorage.setItem(
        "promoStatus",
        JSON.stringify({
          endTime: newEndTime,
          lastPromoTime: now,
        })
      );
      return newEndTime;
    }

    return endTime;
  };

  useEffect(() => {
    const endTime = checkPromoStatus();

    const timer = setInterval(() => {
      const remaining = endTime - Date.now();

      if (remaining <= 0) {
        // 当倒计时结束时，重新检查状态并开始新的促销
        const newEndTime = checkPromoStatus();
        const newRemaining = newEndTime - Date.now();

        const minutes = Math.floor(newRemaining / (60 * 1000));
        const seconds = Math.floor((newRemaining % (60 * 1000)) / 1000);
        const milliseconds = Math.floor((newRemaining % 1000) / 10);

        setTimeLeft({ minutes, seconds, milliseconds });
        setIsPromotionActive(true); // 保持促销活动状态
        return;
      }

      const minutes = Math.floor(remaining / (60 * 1000));
      const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
      const milliseconds = Math.floor((remaining % 1000) / 10);

      setTimeLeft({ minutes, seconds, milliseconds });
      setIsPromotionActive(true);
    }, 10);

    return () => clearInterval(timer);
  }, []);

  const handleUpgradeClick = async () => {
    setIsLoading(true);
    try {
      const response = await subscriptionService.createCheckoutSession(
        plan,
        promoCode,
        "subscription",
        "promotion_card"
      );

      // 添加 umami 收入追踪
      if (response.status === 200) {
        // 构造具体的事件名称用于收入报告分组
        const planType = isYearlyPlan ? "yearly" : "monthly";
        const tier = plan?.includes("pro") ? "pro" : "basic";
        const eventName = `revenue_${tier}_${planType}_promo`;

        trackEvent(eventName, {
          revenue: discountedPrice,
          currency: "USD",
        });
      }

      const url = response.data.url;
      if (url) {
        safeOpenUrl(url);
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    t("features.minutes"),
    t("features.model"),
    // t("features.export"),
    t("features.speakerIdentification"),
    t("features.support"),
  ];

  return (
    <Card className="mb-8 bg-white/90 backdrop-blur-sm border border-slate-200/50 shadow-xl overflow-hidden">
      <CardContent className="p-8 relative">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-slate-50 rounded-full -ml-12 -mb-12"></div>

        <div className="flex items-center justify-between relative z-10">
          <div className="flex-1">
            {/* Header with Badge and Title */}
            <div className="flex items-center gap-3 mb-6">
              <Badge className="bg-amber-100 text-amber-700 border-amber-200">
                <Sparkles className="w-3 h-3 mr-1" />
                {isPromotionActive ? t("limitedTimeOffer") : t("regularPrice")}
              </Badge>
              <h2 className="text-2xl font-medium text-slate-900">
                {isPromotionActive
                  ? t("discountedYearlyPlan")
                  : t("yearlyBasicPlan")}
              </h2>
            </div>

            {/* Pricing */}
            <div className="flex items-baseline gap-4 mb-6">
              <div className="flex items-center gap-2">
                {isPromotionActive && (
                  <>
                    <span className="text-lg line-through text-slate-500">
                      ${originalPrice.toFixed(2)}
                    </span>
                    <Badge className="bg-amber-100 text-amber-700 border-amber-200 px-2 py-1">
                      50% OFF
                    </Badge>
                  </>
                )}
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-semibold text-slate-900">
                  ${discountedPrice.toFixed(2)}
                </span>
                <span className="text-lg text-slate-600">
                  {isYearlyPlan ? t("perYear") : t("perMonth")}
                </span>
                {isYearlyPlan && (
                  <span className="text-sm text-slate-500">
                    (
                    {t("justPerMonth", {
                      price: (discountedPrice / 12).toFixed(2),
                    })}
                    )
                  </span>
                )}
              </div>
            </div>

            {/* Features - Two columns */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              {features.map((feature, i) => (
                <div key={i} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-slate-700">{feature}</span>
                </div>
              ))}
            </div>

            {/* Action Button */}
            <Button
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-8"
              onClick={handleUpgradeClick}
              disabled={isLoading}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              {isLoading ? t("processing") : t("upgradeNow")}
            </Button>
          </div>

          {/* Timer Section */}
          <div className="ml-8">
            {isPromotionActive && (
              <div className="bg-slate-50/80 backdrop-blur-sm rounded-2xl p-6 text-center border border-slate-200/50">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Timer className="w-5 h-5 text-slate-600" />
                  <span className="text-lg text-slate-700">Limited Time</span>
                </div>
                <div className="flex gap-2 text-2xl font-mono text-slate-900">
                  <div className="bg-white border border-slate-200 rounded-lg px-3 py-2 shadow-sm">
                    {String(timeLeft.minutes).padStart(2, "0")}
                  </div>
                  <span className="text-slate-400">:</span>
                  <div className="bg-white border border-slate-200 rounded-lg px-3 py-2 shadow-sm">
                    {String(timeLeft.seconds).padStart(2, "0")}
                  </div>
                  <span className="text-slate-400">:</span>
                  <div className="bg-white border border-slate-200 rounded-lg px-3 py-2 shadow-sm">
                    {String(timeLeft.milliseconds).padStart(2, "0")}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
