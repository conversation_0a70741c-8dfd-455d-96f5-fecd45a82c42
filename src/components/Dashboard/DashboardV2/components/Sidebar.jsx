import { Link } from "@/components/Common/Link";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import { useAuthStore } from "@/stores/useAuthStore";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import ProfileEntryV2 from "./ProfileEntryV2";
import SidebarMenu from "./SidebarMenu";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Clock, Zap, ChevronRight, HardDrive } from "lucide-react";
import { trackAnonymousEvent, trackEvent } from "@/lib/analytics";
import Logo from "@/components/Logo";
import { storageService } from "@/services/storageService";
import { ANONYMOUS_USER_LIMITS } from "@/constants/file";
import { useRouter } from "@/i18n/navigation";
import {
  calculateTotalRemainingMinutes,
  calculateStorageData,
} from "@/lib/planUtils";

export function Sidebar({ className, isAnonymous, onFolderChange }) {
  const t = useTranslations("dashboard.sidebar");
  const { user } = useAuthStore();
  const { openDialog } = useUpgradeDialogStore();
  const { entitlements, summary, fetchEntitlements } = useEntitlementsStore();
  const router = useRouter();
  const [anonymousUsage, setAnonymousUsage] = useState({ totalMinutes: 0 });

  // 读取匿名用户使用情况
  useEffect(() => {
    if (isAnonymous) {
      const usage = storageService.getAnonymousTranscriptionCount();
      setAnonymousUsage(usage);
    }
  }, [isAnonymous]);

  // 获取登录用户的使用情况数据
  useEffect(() => {
    if (!isAnonymous && user) {
      fetchEntitlements();
    }
  }, [isAnonymous, user, fetchEntitlements]);

  const handleSignInClick = () => {
    trackAnonymousEvent("signin_click", {
      source: "sidebar",
    });
  };

  // 处理点击 Current Plan 卡片跳转到 settings 页面的 usage 区域
  const handlePlanCardClick = () => {
    trackEvent("sidebar_plan_card_click", {
      source: "sidebar",
    });
    router.push("/settings#usage");
  };

  // 获取计划显示信息
  const getPlanInfo = () => {
    if (!user) return { planName: "Free", showUpgrade: true };

    const planName = user?.primaryPlan || "Free";

    // 如果是 Free 或 Basic 计划，显示 Upgrade Plan 按钮
    // 如果是 Pro 或 AppSumo 用户，显示 Buy More Minutes 按钮
    const isProOrAppSumo =
      user?.primaryPlan?.toLowerCase().includes("pro") ||
      user?.primaryPlanDetail?.isAppsumo;
    const showUpgrade = !isProOrAppSumo;

    return { planName, showUpgrade };
  };

  // 处理计划按钮点击
  const handlePlanButtonClick = () => {
    const { showUpgrade } = getPlanInfo();

    if (showUpgrade) {
      // 显示升级对话框，默认到 yearly 标签页
      trackEvent("sidebar_plan_click", {
        action: "upgrade",
      });
      openDialog({
        source: "sidebar",
        defaultPlanType: "yearly",
      });
    } else {
      // 显示购买更多分钟对话框，默认到 onetime 标签页
      trackEvent("sidebar_plan_click", {
        action: "buy_more",
      });
      openDialog({
        source: "sidebar",
        defaultPlanType: "onetime",
      });
    }
  };

  return (
    <div
      className={cn(
        "w-full md:w-[260px] bg-white/80 backdrop-blur-xl border-r border-slate-200/50 shadow-xl flex flex-col py-4 h-full",
        className
      )}
    >
      {/* Header */}
      <div className="px-2 flex-shrink-0">
        <div className="mb-5">
          <Logo
            linkProps={{
              href: isAnonymous ? "/" : "/dashboard",
              className: "hover:opacity-90 transition-opacity block",
            }}
          />
        </div>

        {/* Current Plan Section - New Layout */}
        {!isAnonymous && (
          <div
            className="rounded-xl p-4 shadow-sm border border-gray-100 mb-6 cursor-pointer hover:bg-[#6366f1]/5 transition-colors"
            onClick={handlePlanCardClick}
          >
            {/* Header with Plan name and arrow */}
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm font-semibold text-gray-700">
                {t("plan.currentPlan")}
              </span>
              <div className="flex items-center gap-2">
                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full">
                  {getPlanInfo().planName}
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Usage Information */}
            <div className="space-y-3">
              {/* Minutes Section */}
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-2">
                  <span>
                    {t("plan.used")}: {summary?.consumedCredits || 0}
                  </span>
                  <span>
                    {t("plan.total")}: {summary?.totalCredits || 0}
                  </span>
                </div>
                <div className="h-2 bg-indigo-100 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-indigo-600 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        summary?.totalCredits
                          ? ((summary.consumedCredits || 0) /
                              summary.totalCredits) *
                            100
                          : 0
                      }%`,
                    }}
                  />
                </div>
              </div>

              {/* Storage Section - Only for AppSumo users */}
              {user?.primaryPlanDetail?.isAppsumo && (
                <div>
                  <div className="flex justify-between text-xs text-gray-500 mb-2">
                    <span>
                      {t("plan.storageUsed")}:{" "}
                      {calculateStorageData(summary).usedGB} GB
                    </span>
                    <span>
                      {t("plan.storageTotal")}:{" "}
                      {calculateStorageData(summary).totalGB} GB
                    </span>
                  </div>
                  <div className="h-2 bg-green-100 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-600 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          calculateStorageData(summary).totalGB
                            ? (calculateStorageData(summary).usedGB /
                                calculateStorageData(summary).totalGB) *
                              100
                            : 0
                        }%`,
                      }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Upgrade Plan Button */}
            <Button
              className="w-full bg-[#6366f1] hover:bg-[#5855eb] text-white font-medium rounded-full shadow-sm text-sm h-10 mt-4"
              onClick={(e) => {
                e.stopPropagation(); // 防止触发卡片点击事件
                handlePlanButtonClick();
              }}
            >
              {getPlanInfo().showUpgrade
                ? t("plan.upgradePlan")
                : t("plan.buyMoreMinutes")}
            </Button>
          </div>
        )}

        {/* Anonymous User Plan Section */}
        {isAnonymous && (
          <Card className="bg-gradient-to-r from-marketing-50 to-marketing-100 border-marketing-200 mb-6">
            <CardContent className="p-4 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-marketing-800">
                  {t("plan.currentPlan")}
                </span>
                <span className="text-xs font-medium bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
                  {t("plan.guest")}
                </span>
              </div>

              {/* Progress Section */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-1 text-marketing-700">
                    <Clock className="w-3 h-3" />
                    <span>{t("plan.transcription")}</span>
                  </div>
                  <span className="text-marketing-800 font-medium">
                    {anonymousUsage.totalMinutes}/
                    {ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES} min
                  </span>
                </div>
                <div className="relative">
                  <div className="w-full bg-orange-100 rounded-full h-3 overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-orange-400 to-orange-500 rounded-full transition-all duration-300 ease-out shadow-sm"
                      style={{
                        width: `${
                          (anonymousUsage.totalMinutes /
                            ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES) *
                          100
                        }%`,
                      }}
                    />
                  </div>
                  {/* Optional: Add a subtle glow effect */}
                  <div
                    className="absolute top-0 h-full bg-gradient-to-r from-orange-300 to-orange-400 rounded-full opacity-50 blur-sm transition-all duration-300"
                    style={{
                      width: `${
                        (anonymousUsage.totalMinutes /
                          ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES) *
                        100
                      }%`,
                    }}
                  />
                </div>
                <div className="flex items-center space-x-1 text-xs text-marketing-600">
                  <Zap className="w-3 h-3" />
                  <span>{t("plan.signUpForFreeMinutes")}</span>
                </div>
              </div>

              {/* CTA */}
              <div className="py-1.5">
                <Link href="/auth/signup">
                  <Button
                    size="sm"
                    className="w-full bg-marketing-600 hover:bg-marketing-700 text-white"
                    onClick={handleSignInClick}
                  >
                    {t("plan.signUpFree")}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* SidebarMenu with flex-1 to take available space - Hidden for anonymous users */}
      <div className="flex-1 min-h-0">
        <SidebarMenu
          isAnonymous={isAnonymous}
          className="h-full"
          onFolderChange={onFolderChange}
        />
      </div>

      {/* Footer - ProfileEntry - Hidden for anonymous users */}
      {!isAnonymous && (
        <div className="px-2 flex-shrink-0">
          <ProfileEntryV2 />
        </div>
      )}
    </div>
  );
}
