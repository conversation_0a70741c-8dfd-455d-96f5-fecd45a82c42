"use client";

import { DashboardPromotionCard } from "@/components/Dashboard/DashboardV2/components/PromotionCard";
import { useState, useCallback, useRef, useEffect } from "react";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import UploadDialog from "@/components/Dashboard/UploadDialog";
import { YouTubeUploadDialog } from "@/components/Dashboard/YouTubeUpload";
import { transcriptionService } from "@/services/api/transcriptionService";

import { FileList } from "./components/FileList";
import { useAuthStore } from "@/stores/useAuthStore";
import supabase from "@/lib/supabaseClient";

import { useRouter } from "@/i18n/navigation";
import LimitReachedDialog from "@/components/Dashboard/LimitReachedDialog";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import AppSumoWelcomeDialog from "@/components/Dashboard/AppSumoWelcomeDialog";
import AppSumoActivationErrorDialog from "@/components/Dashboard/AppSumoActivationErrorDialog";
import { useDashboardInitialize } from "@/hooks/useDashboardInitialize";

import { useUserLimits } from "@/hooks/useUserLimits";
import FreeUserUpgradeDialog from "@/components/Dialog/FreeUserUpgradeDialog";

export default function DashboardV2({ selectedFolderId = "all" }) {
  const router = useRouter();
  const { fetchEntitlements } = useEntitlementsStore();
  const [showUpload, setShowUpload] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitMessage, setLimitMessage] = useState("");
  const [upgradeSource, setUpgradeSource] = useState("dashboard");
  const { openDialog } = useUpgradeDialogStore();
  const [showAppSumoWelcomeDialog, setShowAppSumoWelcomeDialog] =
    useState(false);
  const [showFreeUserUpgradeDialog, setShowFreeUserUpgradeDialog] =
    useState(false);
  const [upgradeLimitType, setUpgradeLimitType] = useState(null);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const youtubeDialogRef = useRef(null);
  const { user, appsumoActivationError, clearAppSumoActivationError } =
    useAuthStore();
  const { checkLimits } = useUserLimits();
  const shouldShowPromoCard =
    user && !user.hasActiveSubscription && !user.primaryPlanDetail?.isAppsumo;

  // Define all hooks and callbacks first, before any conditional returns
  const handleUploadSuccess = useCallback(
    async (transcription) => {
      setShowUpload(false);
      try {
        // Create transcription task
        await transcriptionService.doTranscription(transcription.id);
        router.push(`/transcriptions/${transcription.id}`);
      } catch (error) {
        console.error("Failed to create transcription task:", error);
      }
    },
    [router]
  );

  const handleYouTubeSubmitSuccess = useCallback(
    (transcription) => {
      router.push(`/transcriptions/${transcription.id}`);
    },
    [router]
  );

  const onUploadClick = useCallback(async () => {
    // Check if user is free user (using hasPaidPlan field)
    const isFreeUser = user && !user.hasPaidPlan;

    if (isFreeUser) {
      setIsCheckingLimits(true);
      try {
        // Check user limits before showing upload dialog
        const { canProceed, limitType } = await checkLimits();

        if (!canProceed) {
          setUpgradeLimitType(limitType);
          setShowFreeUserUpgradeDialog(true);
          return;
        }
      } finally {
        setIsCheckingLimits(false);
      }
    }

    setShowUpload(true);
    fetchEntitlements();
  }, [fetchEntitlements, checkLimits, user]);

  const onYouTubeClick = useCallback(async () => {
    // Check if user is free user (using hasPaidPlan field)
    const isFreeUser = user && !user.hasPaidPlan;

    if (isFreeUser) {
      setIsCheckingLimits(true);
      try {
        // Check user limits before showing YouTube dialog
        const { canProceed, limitType } = await checkLimits();

        if (!canProceed) {
          setUpgradeLimitType(limitType);
          setShowFreeUserUpgradeDialog(true);
          return;
        }
      } finally {
        setIsCheckingLimits(false);
      }
    }

    youtubeDialogRef.current?.openDialog();
    fetchEntitlements();
  }, [fetchEntitlements, checkLimits, user]);

  useEffect(() => {
    const checkAuth = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session || session?.user?.is_anonymous) {
        router.replace("/auth/signin");
      } else {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user, router]);

  // 使用共享的 Dashboard 初始化逻辑
  useDashboardInitialize({
    setShowLimitDialog,
    setLimitMessage,
    setUpgradeSource,
    setShowAppSumoWelcomeDialog,
    source: "dashboard",
    isLoading,
  });

  // 处理升级按钮点击
  const handleUpgradeClick = () => {
    setShowLimitDialog(false);
    openDialog({
      source: upgradeSource,
      defaultPlanType: "yearly",
    });
  };

  return (
    <div className="w-full">
      <div className="flex flex-col gap-4 md:gap-6 h-full min-h-[600px]">
        {/* Show promotion card above FileList if needed */}
        {shouldShowPromoCard && (
          <div className="w-full">
            <div className="bg-white rounded-xl">
              <DashboardPromotionCard />
            </div>
          </div>
        )}

        <div className="flex-1 bg-white rounded-xl flex flex-col">
          <FileList
            selectedFolderId={selectedFolderId}
            onUploadClick={onUploadClick}
            onYouTubeClick={onYouTubeClick}
            isCheckingLimits={isCheckingLimits}
          />
        </div>
      </div>

      {/* Upload Dialogs */}
      <UploadDialog
        isOpen={showUpload}
        onOpenChange={setShowUpload}
        onUploadSuccess={handleUploadSuccess}
        selectedFolderId={selectedFolderId}
      />
      <YouTubeUploadDialog
        ref={youtubeDialogRef}
        onTranscribeSubmit={handleYouTubeSubmitSuccess}
        selectedFolderId={selectedFolderId}
      />

      {/* 限制提示弹窗 */}
      <LimitReachedDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        onUpgrade={handleUpgradeClick}
        source={upgradeSource}
        message={limitMessage}
      />

      {/* 免费用户升级弹窗 */}
      <FreeUserUpgradeDialog
        isOpen={showFreeUserUpgradeDialog}
        onOpenChange={setShowFreeUserUpgradeDialog}
        limitType={upgradeLimitType}
      />

      {/* AppSumo 欢迎弹窗 */}
      <AppSumoWelcomeDialog
        isOpen={showAppSumoWelcomeDialog}
        onClose={() => setShowAppSumoWelcomeDialog(false)}
        user={user}
      />

      {/* AppSumo 激活错误弹窗 */}
      <AppSumoActivationErrorDialog
        isOpen={!!appsumoActivationError}
        onClose={clearAppSumoActivationError}
        errorCode={appsumoActivationError}
      />
    </div>
  );
}
